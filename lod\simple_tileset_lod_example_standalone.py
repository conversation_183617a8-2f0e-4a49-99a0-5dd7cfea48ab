"""
简单Tileset LOD示例 - Standalone版本
基于tileset.json文件构建八叉树并进行LOD管理
支持Isaac Sim SimulationApp和直接运行
"""

import asyncio
import time
import math
import sys
import os
import json

# Isaac Sim standalone 支持
from isaacsim import SimulationApp
# HUD 配置标志位
DISP_FPS        = 1 << 0
DISP_RESOLUTION = 1 << 3
DISP_DEV_MEM    = 1 << 13
DISP_HOST_MEM   = 1 << 14

config = {
    "width": 1280,
    "height": 720,
    "headless": False,
    "display_options": DISP_FPS | DISP_RESOLUTION | DISP_DEV_MEM | DISP_HOST_MEM,
}

simulation_app = SimulationApp(launch_config=config)

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd
import omni.kit.app
import omni.timeline
import omni.kit.commands

# 导入LOD调度器
try:
    from lod_scheduler import LODScheduler, BoundingBox, LODLevel, LODTile
    print("✓ Successfully imported lod_scheduler")
except ImportError as e:
    print(f"⚠️ Warning: Could not import lod_scheduler: {e}")
    print("Will use built-in LOD classes instead")
    sys.exit(1)

# 配置类
class TilesetConfig:
    """Tileset LOD配置类"""
    def __init__(self):
        # Tileset文件配置
        self.tileset_path = os.path.join(current_dir, "tileset_data", "florenz_village", "tileset_simple.json")

        self.usd_file_path = "C:/test-usd-path"  # 默认路径，需要用户修改
        
        # 相机配置
        self.camera_path = "/World/Camera"
        
        # LOD切换配置（SSE与距离阈值）
        self.auto_mode = "movement"  # "movement" 或 "timer"
        self.timer_interval = 1.0  # 主线程事件订阅间隔（秒）
        self.debug_info = True  # 是否输出调试信息
        
        # 运行时控制
        self.auto_start_runtime = True  # 是否自动开启运行时
        
        # 相机移动模拟配置
        self.camera_start_position = Gf.Vec3f(0, 0, 50)  # 相机起始位置
        self.camera_target_position = Gf.Vec3f(20, 0, 100)  # 相机目标位置
        self.camera_movement_duration = 30.0  # 移动持续时间（秒）
        self.camera_movement_loop = True  # 是否循环移动
        
        # 每个LOD的几何误差（世界单位，米）
        self.lod_geometric_errors = {
            "High": 1.0,    # 高质量LOD：1.0米几何误差
            "Medium": 4.0,  # 中质量LOD：4.0米几何误差
            "Low": 8.0,     # 低质量LOD：8.0米几何误差
        }

        # SSE配置
        self.maximum_screen_space_error = 32.0  # 最大可接受屏幕像素误差阈值
        self.screen_width = 1920  # 视口宽度（像素）
        self.horizontal_fov = 60.0  # 水平视野角（度）

# 全局变量来跟踪当前LOD状态
current_lod_state = {"last_lod": None, "last_distance": None}
# 全局变量来跟踪实时更新状态
realtime_update_active = False
realtime_update_data = None
# 全局变量来跟踪自动更新订阅
auto_update_subscription = None

def load_tileset_file(tileset_path):
    """加载tileset.json文件"""
    try:
        print(f"Loading tileset file: {tileset_path}")
        
        # 检查文件是否存在
        if not os.path.exists(tileset_path):
            print(f"ERROR: Tileset file not found: {tileset_path}")
            return None
        
        with open(tileset_path, 'r', encoding='utf-8') as f:
            tileset_data = json.load(f)
        
        print(f"Successfully loaded tileset file: {tileset_path}")
        return tileset_data
        
    except Exception as e:
        print(f"ERROR: Failed to load tileset file: {e}")
        return None

def create_stage_and_load_tileset_prims(tileset_data, config: 'TilesetConfig' = None):
    """创建stage并根据tileset数据加载USDZ prims"""
    print("Creating stage and loading tileset USDZ prims...")
    
    # 获取或创建stage
    context = omni.usd.get_context()

    # 首先尝试获取现有的stage
    stage = context.get_stage()

    if stage:
        print("Using existing stage")
        # 清理现有stage的内容（可选）
        # stage.RemovePrim("/World")
    else:
        print("Creating new stage...")
        # 创建新的stage文件
        import tempfile
        temp_dir = tempfile.gettempdir()
        stage_path = os.path.join(temp_dir, "TilesetLODExample.usda").replace("\\", "/")

        try:
            # 使用omni.usd创建新stage
            success = context.new_stage()
            if success:
                stage = context.get_stage()
                print(f"Successfully created new stage")
            else:
                print("Failed to create new stage, creating in-memory stage")
                stage = Usd.Stage.CreateNew()
        except Exception as e:
            print(f"Error creating stage: {e}, creating in-memory stage")
            stage = Usd.Stage.CreateNew()

    if not stage:
        print("ERROR: Failed to create or get stage")
        return None, None

    # 设置物理场景以避免物理系统错误
    setup_physics_scene(stage)

    # 获取tileset的基础路径
    base_path = os.path.dirname(config.tileset_path) if config else os.path.dirname(tileset_data.get('uri', ''))

    # 解析tileset层次结构并创建prims
    root_tile = tileset_data.get('root')
    if not root_tile:
        print("ERROR: No root tile found in tileset")
        return None, None
    
    # 解析根节点的边界框
    root_bounds = parse_tileset_bounding_box(root_tile.get('boundingVolume', {}))
    if not root_bounds:
        print("ERROR: Failed to parse root bounding box")
        return None, None
    
    print(f"Tileset root bounds: {root_bounds.min_point} to {root_bounds.max_point}")
    
    # 创建区域容器
    region_path = "/World/TilesetRegion"
    region_prim = stage.DefinePrim(region_path)
    region_prim.SetTypeName("Xform")
    
    # 递归创建tileset层次结构的prims
    created_prims = []
    create_tileset_prims_hierarchy(stage, root_tile, region_path, base_path, created_prims)
    
    print(f"Created {len(created_prims)} tileset prims")
    
    # 创建相机
    camera_path = "/World/Camera"
    camera = UsdGeom.Camera.Define(stage, camera_path)
    
    # 设置相机位置
    center = root_bounds.center
    region_size = root_bounds.size
    max_dimension = max(region_size[0], region_size[1], region_size[2])
    camera_distance = max_dimension * 0.3  # 设置为区域最大尺寸的30%
    
    camera_position = Gf.Vec3f(
        center[0], 
        center[1], 
        center[2] + camera_distance
    )
    
    xformable = UsdGeom.Xformable(camera)
    xformable.AddTranslateOp().Set(camera_position)
    
    print(f"Created camera at {camera_path}")
    print(f"Camera position: {camera_position}")
    print(f"Distance to region center: {camera_distance:.1f}")
    
    print("Tileset stage and prims created successfully!")
    print(f"Region bounds: {root_bounds.min_point} to {root_bounds.max_point}")
    print(f"Region size: {root_bounds.size}")
    print(f"Region center: {center}")
    
    return stage, root_bounds

def parse_tileset_bounding_box(bounding_volume):
    """解析tileset中的边界框"""
    if 'box' in bounding_volume:
        # 3D Tiles格式的box
        box = bounding_volume['box']
        if len(box) >= 12:
            center = Gf.Vec3f(box[0], box[1], box[2])
            
            # 计算半轴长度
            x_axis = Gf.Vec3f(box[3], box[4], box[5])
            y_axis = Gf.Vec3f(box[6], box[7], box[8])
            z_axis = Gf.Vec3f(box[9], box[10], box[11])
            
            # 计算边界框的最小和最大点
            x_extent = abs(x_axis[0]) + abs(y_axis[0]) + abs(z_axis[0])
            y_extent = abs(x_axis[1]) + abs(y_axis[1]) + abs(z_axis[1])
            z_extent = abs(x_axis[2]) + abs(y_axis[2]) + abs(z_axis[2])
            
            min_point = Gf.Vec3f(
                center[0] - x_extent,
                center[1] - y_extent,
                center[2] - z_extent
            )
            max_point = Gf.Vec3f(
                center[0] + x_extent,
                center[1] + y_extent,
                center[2] + z_extent
            )
            
            return BoundingBox(min_point, max_point)
    
    elif 'sphere' in bounding_volume:
        # 球形边界
        sphere = bounding_volume['sphere']
        if len(sphere) >= 4:
            center = Gf.Vec3f(sphere[0], sphere[1], sphere[2])
            radius = sphere[3]
            min_point = Gf.Vec3f(center[0] - radius, center[1] - radius, center[2] - radius)
            max_point = Gf.Vec3f(center[0] + radius, center[1] + radius, center[2] + radius)
            return BoundingBox(min_point, max_point)
    
    return None

def create_tileset_prims_hierarchy(stage, tile_data, parent_path, base_path, created_prims, depth=0):
    """递归创建tileset层次结构的prims"""
    try:
        # 获取几何误差
        geometric_error = tile_data.get('geometricError', 1.0)
        
        # 获取内容URI
        content = tile_data.get('content', {})
        uri = content.get('uri', '')
        
        # 根据几何误差确定LOD级别
        if geometric_error <= 1.5:
            lod_name = "High"
        elif geometric_error <= 5.0:
            lod_name = "Medium"
        else:
            lod_name = "Low"
        
        # 构建完整的文件路径
        if uri:
            if os.path.isabs(uri):
                usdz_path = uri
            else:
                usdz_path = os.path.join(base_path, uri).replace('\\', '/')
        else:
            print(f"Warning: No URI found for tile at depth {depth}")
            return
        
        # 创建LOD层级容器
        lod_path = f"{parent_path}/LOD_{lod_name}"
        lod_prim = stage.GetPrimAtPath(lod_path)
        
        if not lod_prim or not lod_prim.IsValid():
            lod_prim = stage.DefinePrim(lod_path)
            lod_prim.SetTypeName("Xform")
            
            # 解析边界框
            bounding_box = parse_tileset_bounding_box(tile_data.get('boundingVolume', {}))
            if bounding_box:
                # 设置边界属性
                lod_prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3).Set(bounding_box.min_point)
                lod_prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3).Set(bounding_box.max_point)
            
            lod_prim.CreateAttribute("omni:nurec:lod:level", Sdf.ValueTypeNames.String).Set(lod_name)
            lod_prim.CreateAttribute("tileset:geometricError", Sdf.ValueTypeNames.Float).Set(geometric_error)
            
            # 加载USDZ文件
            usdz_loaded = load_usdz_file_into_stage(usdz_path, f"{lod_path}/USDZContent")
            
            if usdz_loaded:
                # 设置初始可见性（只有最低质量LOD默认可见）
                imageable = UsdGeom.Imageable(lod_prim)
                vis_attr = imageable.GetVisibilityAttr()
                visible = (lod_name == "Low")  # 默认显示最低质量
                vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)
                
                created_prims.append({
                    'path': lod_path,
                    'lod_name': lod_name,
                    'geometric_error': geometric_error,
                    'uri': uri,
                    'usdz_path': usdz_path,
                    'visible': visible
                })
                
                print(f"Created LOD_{lod_name} with USDZ at {lod_path}")
                print(f"  GeometricError: {geometric_error}, URI: {uri}")
                print(f"  USDZ Path: {usdz_path}")
                print(f"  Visible: {visible}")
            else:
                print(f"Failed to load USDZ for LOD_{lod_name} at {lod_path}")
        
        # 递归处理子瓦片
        children = tile_data.get('children', [])
        for child_tile in children:
            create_tileset_prims_hierarchy(stage, child_tile, parent_path, base_path, created_prims, depth + 1)
            
    except Exception as e:
        print(f"Error creating tileset prim at depth {depth}: {e}")
        import traceback
        traceback.print_exc()

def load_usdz_file_into_stage(usdz_path, target_path):
    """将USDZ文件加载到stage中的指定路径"""
    try:
        stage = omni.usd.get_context().get_stage()
        
        # 检查文件是否存在
        if not os.path.exists(usdz_path):
            print(f"Warning: USDZ file not found: {usdz_path}")
            return False
        
        # 使用USD的SdfLayer来引用USDZ文件
        layer = Sdf.Layer.FindOrOpen(usdz_path)
        if layer:
            # 创建引用
            prim = stage.DefinePrim(target_path)
            prim.GetReferences().AddReference(usdz_path)
            print(f"Successfully referenced USDZ file: {usdz_path} at {target_path}")
            return True
        else:
            print(f"Warning: Could not open USDZ file: {usdz_path}")
            return False
    except Exception as e:
        print(f"Error loading USDZ file {usdz_path}: {e}")
        return False

def start_runtime():
    """启动运行时（播放模式）"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.play()
        print("Runtime started (Play mode)")
        return True
    except Exception as e:
        print(f"ERROR: Failed to start runtime: {e}")
        return False

def stop_runtime():
    """停止运行时"""
    try:
        timeline = omni.timeline.get_timeline_interface()
        timeline.stop()
        print("Runtime stopped")
        return True
    except Exception as e:
        print(f"ERROR: Failed to stop runtime: {e}")
        return False

def setup_physics_scene(stage):
    """设置基本的物理场景以避免物理系统错误"""
    try:
        from pxr import UsdPhysics

        # 检查是否已有物理场景
        physics_scene_path = "/World/PhysicsScene"
        physics_scene_prim = stage.GetPrimAtPath(physics_scene_path)

        if not physics_scene_prim or not physics_scene_prim.IsValid():
            print("Creating physics scene...")
            # 创建物理场景
            physics_scene_prim = stage.DefinePrim(physics_scene_path, "PhysicsScene")
            physics_scene = UsdPhysics.Scene(physics_scene_prim)

            # 设置基本的物理参数
            physics_scene.CreateGravityDirectionAttr().Set(Gf.Vec3f(0.0, 0.0, -1.0))
            physics_scene.CreateGravityMagnitudeAttr().Set(9.81)

            print("Physics scene created successfully")
        else:
            print("Physics scene already exists")

        return True

    except Exception as e:
        print(f"Warning: Failed to setup physics scene: {e}")
        print("Continuing without physics scene...")
        return False

def wait_for_render_initialization(max_wait_time=10.0):
    """等待渲染系统初始化完成"""
    print("Waiting for render system initialization...")

    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        # 更新 Isaac Sim 以推进渲染初始化
        simulation_app.update()

        # 检查是否有可用的 stage
        stage = omni.usd.get_context().get_stage()
        if stage:
            print("Stage is ready")
            # 设置物理场景以避免错误
            setup_physics_scene(stage)
            return True

        time.sleep(0.1)  # 短暂等待

    print(f"Warning: Render initialization timeout after {max_wait_time}s")
    return False

def update_tileset_lod_visibility(stage, region_bounds, scheduler, verbose=True, config: 'TilesetConfig' = None):
    """使用调度器更新tileset LOD可见性"""
    if verbose:
        print("\n=== Updating Tileset LOD Visibility ===")

    try:
        # 使用调度器的SSE方法更新LOD
        selected_lod, center_distance, representative_sse = scheduler.update_lod_visibility_by_sse(region_bounds, verbose)

        if selected_lod:
            # 更新所有LOD级别的可见性
            lod_levels = ["High", "Medium", "Low"]
            for lod_name in lod_levels:
                lod_path = f"/World/TilesetRegion/LOD_{lod_name}"
                lod_prim = stage.GetPrimAtPath(lod_path)

                if lod_prim:
                    # 使用UsdGeom.Imageable设置可见性
                    imageable = UsdGeom.Imageable(lod_prim)
                    vis_attr = imageable.GetVisibilityAttr()

                    # 设置可见性：只有目标LOD可见，其他都隐藏
                    visible = (lod_name == selected_lod)
                    vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)

                    if verbose:
                        print(f"  LOD_{lod_name}: {'Visible' if visible else 'Hidden'}")
                else:
                    if verbose:
                        print(f"  LOD_{lod_name}: Not found")

            return selected_lod, center_distance, representative_sse
        else:
            if verbose:
                print("Failed to update LOD visibility")
            return None, None, None

    except Exception as e:
        if verbose:
            print(f"Error updating tileset LOD visibility: {e}")
        return None, None, None

def _start_mainthread_update_subscription(stage, region_bounds, scheduler, config, update_interval: float = 1.0):
    """使用 Kit 的 update 事件在主线程上进行节流更新"""
    global realtime_update_active, realtime_update_data, current_lod_state, auto_update_subscription

    # 防止重复订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None

    import omni.kit.app
    app = omni.kit.app.get_app()
    stream = app.get_update_event_stream()

    last_ts = time.time()
    accumulator = 0.0

    def on_update(e):
        nonlocal last_ts, accumulator
        if not realtime_update_active:
            return

        now = time.time()
        dt = now - last_ts
        last_ts = now
        accumulator += dt

        if accumulator < update_interval:
            return
        accumulator = 0.0

        try:
            # 更新LOD并获取指标
            current_lod, distance, screen_error = update_tileset_lod_visibility(
                stage, region_bounds, scheduler, verbose=False, config=config
            )
            if (current_lod and
                (current_lod_state["last_lod"] != current_lod or
                abs((current_lod_state["last_distance"] or 0) - distance) > 10.0)):
                print(f"🔄 Tileset LOD switched to {current_lod} (distance: {distance:.1f}, screen: {screen_error:.2f})")
                current_lod_state["last_lod"] = current_lod
                current_lod_state["last_distance"] = distance
        except Exception as ex:
            print(f"Error in main-thread update: {ex}")

    auto_update_subscription = stream.create_subscription_to_pop(on_update, name="TilesetLODUpdateOnMainThread")

def start_automatic_tileset_lod_switching(config: 'TilesetConfig'):
    """启动自动tileset LOD切换"""
    global realtime_update_active, realtime_update_data, auto_update_subscription

    print("\n=== Starting Automatic Tileset LOD Switching ===")

    # 加载tileset文件
    tileset_data = load_tileset_file(config.tileset_path)
    if not tileset_data:
        return None, None, None

    # 创建stage并加载tileset prims
    stage, region_bounds = create_stage_and_load_tileset_prims(tileset_data, config)
    if not stage or not region_bounds:
        return None, None, None

    # 创建LOD调度器
    scheduler = LODScheduler(stage, camera_path=config.camera_path)

    # 配置调度器参数
    scheduler.lod_geometric_errors = config.lod_geometric_errors
    scheduler.maximum_screen_space_error = config.maximum_screen_space_error
    scheduler.screen_width = config.screen_width
    scheduler.horizontal_fov = config.horizontal_fov

    # 从tileset构建八叉树
    scheduler.build_octree_from_tileset(config.tileset_path)

    # 保存数据到全局变量
    realtime_update_active = True

    # 启动主线程事件订阅
    try:
        print("Starting main-thread event-based automatic updates...")
        _start_mainthread_update_subscription(stage, region_bounds, scheduler, config, config.timer_interval)
        print("✅ Automatic tileset LOD switching started using main-thread event subscription!")
        print(f"The system will automatically update LOD every {config.timer_interval} seconds on the main thread.")
        return stage, region_bounds, scheduler
    except Exception as e:
        print(f"Main-thread event subscription failed: {e}")
        return stage, region_bounds, scheduler

def stop_automatic_tileset_lod_switching():
    """停止自动tileset LOD切换"""
    global realtime_update_active, realtime_update_data, auto_update_subscription

    print("\n=== Stopping Automatic Tileset LOD Switching ===")

    # 停止自动更新
    realtime_update_active = False
    realtime_update_data = None

    # 取消订阅
    if auto_update_subscription:
        try:
            auto_update_subscription.unsubscribe()
        except Exception:
            pass
        auto_update_subscription = None
    print("✅ Automatic tileset LOD switching stopped successfully!")

def check_current_tileset_lod_status(stage, region_bounds, scheduler):
    """检查当前tileset LOD状态"""
    print("\n=== Current Tileset LOD Status ===")

    try:
        # 获取相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if not camera:
            print("Camera not found!")
            return

        xformable = UsdGeom.Xformable(camera)
        transform = xformable.GetLocalTransformation()
        camera_position = Gf.Vec3f(transform.ExtractTranslation())

        # 计算距离
        center = region_bounds.center
        distance = math.sqrt(
            (camera_position[0] - center[0])**2 +
            (camera_position[1] - center[1])**2 +
            (camera_position[2] - center[2])**2
        )

        # 使用调度器选择LOD
        selected_lod, lod_info = scheduler.select_lod_by_sse_and_distance(
            region_bounds, camera_position, verbose=True
        )

        print(f"Camera position: {camera_position}")
        print(f"Distance to region center: {distance:.1f}")
        print(f"Selected LOD level: {selected_lod}")

        # 检查各LOD级别的可见性
        print(f"\nLOD visibility status:")
        lod_levels = ["High", "Medium", "Low"]
        for lod_name in lod_levels:
            lod_path = f"/World/TilesetRegion/LOD_{lod_name}"
            lod_prim = stage.GetPrimAtPath(lod_path)

            if lod_prim:
                imageable = UsdGeom.Imageable(lod_prim)
                vis_attr = imageable.GetVisibilityAttr()
                visibility = vis_attr.Get()
                print(f"  - LOD_{lod_name}: {'Visible' if visibility == UsdGeom.Tokens.inherited else 'Hidden'}")
            else:
                print(f"  - LOD_{lod_name}: Not found")

    except Exception as e:
        print(f"Error checking tileset LOD status: {e}")

def manual_tileset_lod_update(stage, region_bounds, scheduler, config):
    """手动更新tileset LOD（用于测试）"""
    print("\n=== Manual Tileset LOD Update ===")

    try:
        # 执行一次LOD更新
        current_lod, distance, screen_error = update_tileset_lod_visibility(
            stage, region_bounds, scheduler, verbose=True, config=config
        )

        if current_lod:
            # 更新状态跟踪
            global current_lod_state
            current_lod_state["last_lod"] = current_lod
            current_lod_state["last_distance"] = distance

            print(f"Manual update completed: {current_lod} LOD (distance: {distance:.1f})")
        else:
            print("Manual update failed")

    except Exception as e:
        print(f"Error in manual tileset LOD update: {e}")

# ============================================================================
# 主执行函数 - Standalone版本
# ============================================================================

def load_usd_stage(usd_file_path):
    """加载USD文件到stage"""
    try:
        print(f"Loading USD file: {usd_file_path}")
        
        # 检查文件是否存在
        import os
        if not os.path.exists(usd_file_path):
            print(f"ERROR: USD file not found: {usd_file_path}")
            return False
        
        # 使用与 auto_camera_optimizer_standalone.py 相同的方法打开stage
        print(f"Opening stage: '{usd_file_path}'")
        omni.usd.get_context().open_stage(usd_file_path)
        stage = omni.usd.get_context().get_stage()
        
        if not stage:
            print(f"ERROR: Failed to open USD file: {usd_file_path}")
            return False
        
        print(f"Successfully loaded USD file: {usd_file_path}")
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to load USD file: {e}")
        return False

def initialize_isaac_sim_environment():
    """初始化Isaac Sim环境，确保所有系统正常工作"""
    print("Initializing Isaac Sim environment...")

    try:
        # 1. 等待渲染系统初始化
        if not wait_for_render_initialization():
            print("WARNING: Render initialization may not be complete")

        # 2. 确保有有效的stage
        context = omni.usd.get_context()
        stage = context.get_stage()

        if not stage:
            print("Creating new stage for Isaac Sim...")
            success = context.new_stage()
            if success:
                stage = context.get_stage()
                print("New stage created successfully")
            else:
                print("Failed to create new stage")
                return False

        # 3. 设置基本的场景结构
        world_prim = stage.GetPrimAtPath("/World")
        if not world_prim or not world_prim.IsValid():
            world_prim = stage.DefinePrim("/World", "Xform")
            print("Created /World prim")

        # 4. 设置物理场景
        setup_physics_scene(stage)

        # 5. 更新一次以确保所有系统同步
        simulation_app.update()

        print("Isaac Sim environment initialized successfully")
        return True

    except Exception as e:
        print(f"Error initializing Isaac Sim environment: {e}")
        return False

def run_tileset_standalone_mode(config):
    """运行tileset standalone模式"""
    print("=== Simple Tileset LOD Example Standalone Mode ===")

    # 1. 检查tileset文件路径
    if not config.tileset_path or not os.path.exists(config.tileset_path):
        print(f"ERROR: Tileset file not found: {config.tileset_path}")
        return None

    # 2. 加载USD文件
    if not load_usd_stage(config.usd_file_path):
        return None

    # 2. 初始化Isaac Sim环境
    # if not initialize_isaac_sim_environment():
    #     print("ERROR: Failed to initialize Isaac Sim environment")
    #     return None

    # 3. 自动启动运行时（如果配置启用）
    if config.auto_start_runtime:
        if not start_runtime():
            print("WARNING: Failed to start runtime, continuing...")

    # 4. 启动自动tileset LOD切换
    print(f"\nStarting Automatic Tileset LOD Switching...")
    stage, region_bounds, scheduler = start_automatic_tileset_lod_switching(config)

    if stage and region_bounds and scheduler:
        print(f"\n=== Tileset standalone mode setup completed ===")
        print(f"Tileset file: {config.tileset_path}")
        print(f"Camera path: {config.camera_path}")
        print(f"Auto mode: {config.auto_mode}")
        print(f"Timer interval: {config.timer_interval}s")
        print(f"Debug info: {config.debug_info}")

        return stage, region_bounds, scheduler
    else:
        print("ERROR: Failed to setup tileset standalone mode")
        return None

def main():
    """主函数 - Tileset Standalone版本，直接运行自动LOD切换"""
    print("Simple Tileset LOD Example - Standalone Version")
    print("=" * 60)
    print("This version runs automatically using tileset.json structure.")
    print("The system will start automatic LOD switching immediately.")
    print("=" * 60)

    try:
        # 创建配置
        config = TilesetConfig()

        config.usdz

        # 直接启动tileset LOD切换
        print("\n🚀 Starting tileset LOD switching...")
        result = run_tileset_standalone_mode(config)

        if result:
            stage, region_bounds, scheduler = result

            print("\n" + "=" * 50)
            print("✅ Tileset LOD switching started successfully!")
            print("\nFeatures:")
            print("- Tileset.json based LOD structure")
            print("- SSE (Screen Space Error) based LOD selection")
            print("- Automatic LOD switching based on camera distance")
            print("- Real-time updates as you move the camera")
            print("- Uses actual USDZ files with visibility control")
            print("\nThe system is now running automatically!")
            print("Move the camera in Omniverse to see LOD changes.")
            print("=" * 50)

                            # 保持脚本运行（可选）
            print("\nPress Ctrl+C to stop...")
            try:
                print("Starting main loop...")
                while True:
                    simulation_app.update()  # 使用 simulation_app.update() 而不是 time.sleep()
                    time.sleep(0.01)  # 短暂延迟以避免过度占用CPU
            except KeyboardInterrupt:
                print("\nStopping...")
                if scheduler:
                    stop_automatic_tileset_lod_switching()

            # 显示初始状态
            # print("\n📊 Initial Tileset LOD Status:")
            # check_current_tileset_lod_status(stage, region_bounds, scheduler)

            # print("\n🎯 System is ready! Move the camera to test LOD switching.")
            # print("You can call the following functions from the console:")
            # print("  - check_current_tileset_lod_status(stage, region_bounds, scheduler)")
            # print("  - manual_tileset_lod_update(stage, region_bounds, scheduler, config)")
            # print("  - stop_automatic_tileset_lod_switching()")
        else:
            print("❌ Failed to start tileset LOD switching")

    except Exception as e:
        print(f"❌ Error starting tileset LOD switching: {e}")
        import traceback
        traceback.print_exc()

# 便捷函数
def start_tileset_lod():
    """便捷函数：启动tileset LOD切换"""
    config = TilesetConfig()
    return run_tileset_standalone_mode(config)

def stop_tileset_lod():
    """便捷函数：停止tileset LOD切换"""
    return stop_automatic_tileset_lod_switching()

# 使用说明
print("\n" + "=" * 60)
print("Simple Tileset LOD Example - Standalone Version")
print("=" * 60)
print("\nThis script supports Isaac Sim standalone mode with tileset.json.")
print("It automatically loads USDZ files and switches LOD based on SSE calculation.")
print("\nFeatures:")
print("- Tileset.json structure parsing")
print("- SSE (Screen Space Error) based LOD selection")
print("- Geometric error and distance range calculation")
print("- Automatic LOD switching based on camera distance")
print("- Real-time updates as you move the camera")
print("- Uses actual USDZ files with visibility control")
print("\nQuick Start:")
print("1. Ensure tileset_simple.json exists in tileset_data/florenz_village/")
print("2. Ensure USDZ files (16.usdz, 18.usdz, 20.usdz) exist in the same directory")
print("3. Run: python simple_tileset_lod_example_standalone.py")
print("\nAvailable functions:")
print("  - start_tileset_lod()    # Start tileset LOD switching")
print("  - stop_tileset_lod()     # Stop tileset LOD switching")
print("\nOr call main() directly from the console")
print("=" * 60)

# 示例使用
if __name__ == "__main__":
    main()
