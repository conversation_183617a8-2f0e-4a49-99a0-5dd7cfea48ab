"""
测试简化后的LOD系统
验证移除八叉树逻辑后的功能
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_simple_lod_functions():
    """测试simple_lod_example_standalone.py的函数"""
    print("=== Testing Simple LOD Functions ===")
    
    try:
        # 测试导入
        from simple_lod_example_standalone import StandaloneConfig
        
        config = StandaloneConfig()
        print("✅ StandaloneConfig imported and created successfully")
        print(f"  USDZ paths: {len(config.usdz_paths)} files")
        print(f"  LOD geometric errors: {config.lod_geometric_errors}")
        print(f"  Max SSE: {config.maximum_screen_space_error}")
        
        # 验证八叉树相关函数已移除
        try:
            from simple_lod_example_standalone import start_octree_lod
            print("❌ start_octree_lod should have been removed")
            return False
        except ImportError:
            print("✅ start_octree_lod correctly removed")
        
        try:
            from simple_lod_example_standalone import demo_octree_compatibility
            print("❌ demo_octree_compatibility should have been removed")
            return False
        except ImportError:
            print("✅ demo_octree_compatibility correctly removed")
        
        # 验证保留的函数
        from simple_lod_example_standalone import start_lod, stop_lod, manual_update
        print("✅ Core single-region functions preserved")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing simple LOD functions: {e}")
        return False

def test_lod_scheduler_deprecation():
    """测试lod_scheduler.py中的废弃功能"""
    print("\n=== Testing LOD Scheduler Deprecation ===")
    
    try:
        from lod_scheduler import LODScheduler
        
        # 创建一个模拟的stage（这里只是测试逻辑）
        class MockStage:
            pass
        
        mock_stage = MockStage()
        scheduler = LODScheduler(mock_stage)
        
        print("✅ LODScheduler created successfully")
        
        # 测试废弃的方法
        print("Testing deprecated build_octree_from_stage...")
        scheduler.build_octree_from_stage()
        print("✅ build_octree_from_stage shows deprecation warning")
        
        # 验证tileset方法仍然存在
        if hasattr(scheduler, 'build_octree_from_tileset'):
            print("✅ build_octree_from_tileset method preserved")
        else:
            print("❌ build_octree_from_tileset method missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing LOD scheduler deprecation: {e}")
        return False

def test_tileset_standalone_config():
    """测试tileset standalone配置"""
    print("\n=== Testing Tileset Standalone Config ===")
    
    try:
        from simple_tileset_lod_example_standalone import TilesetConfig
        
        config = TilesetConfig()
        print("✅ TilesetConfig created successfully")
        print(f"  Tileset path: {config.tileset_path}")
        print(f"  Max SSE: {config.maximum_screen_space_error}")
        print(f"  LOD geometric errors: {config.lod_geometric_errors}")
        
        # 验证tileset文件存在
        if os.path.exists(config.tileset_path):
            print("✅ Tileset file exists")
        else:
            print(f"⚠️ Tileset file not found: {config.tileset_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tileset standalone config: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n=== Testing File Structure ===")
    
    expected_files = [
        "simple_lod_example_standalone.py",
        "simple_tileset_lod_example_standalone.py", 
        "lod_scheduler.py",
        "tileset_data/florenz_village/tileset_simple.json"
    ]
    
    missing_files = []
    for file_path in expected_files:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"Missing files: {missing_files}")
        return False
    
    print("✅ All expected files present")
    return True

def test_functionality_separation():
    """测试功能分离"""
    print("\n=== Testing Functionality Separation ===")
    
    try:
        # 验证simple_lod_example_standalone.py专注于单区域
        from simple_lod_example_standalone import StandaloneConfig
        config = StandaloneConfig()
        
        # 应该有USDZ路径配置
        if hasattr(config, 'usdz_paths') and config.usdz_paths:
            print("✅ Simple LOD has USDZ paths for single-region")
        else:
            print("❌ Simple LOD missing USDZ paths")
            return False
        
        # 验证tileset standalone专注于tileset
        from simple_tileset_lod_example_standalone import TilesetConfig
        tileset_config = TilesetConfig()
        
        # 应该有tileset路径配置
        if hasattr(tileset_config, 'tileset_path') and tileset_config.tileset_path:
            print("✅ Tileset LOD has tileset path")
        else:
            print("❌ Tileset LOD missing tileset path")
            return False
        
        print("✅ Functionality properly separated")
        return True
        
    except Exception as e:
        print(f"❌ Error testing functionality separation: {e}")
        return False

def main():
    """主测试函数"""
    print("Simplified LOD System Test")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_simple_lod_functions,
        test_lod_scheduler_deprecation,
        test_tileset_standalone_config,
        test_functionality_separation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The simplified LOD system is working correctly.")
        print("\nKey Changes Verified:")
        print("✅ Octree functions removed from simple_lod_example_standalone.py")
        print("✅ build_octree_from_stage deprecated in lod_scheduler.py")
        print("✅ Tileset functionality separated into dedicated file")
        print("✅ Single-region LOD functionality preserved")
        print("✅ Proper functionality separation maintained")
        
        print("\nUsage:")
        print("  Single-region LOD: python simple_lod_example_standalone.py")
        print("  Tileset LOD: python simple_tileset_lod_example_standalone.py")
        
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
