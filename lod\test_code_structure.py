"""
测试代码结构（不依赖Isaac <PERSON>）
验证代码修改是否正确
"""

import sys
import os
import ast
import inspect

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_file_structure():
    """测试文件结构"""
    print("=== Testing File Structure ===")
    
    expected_files = [
        "simple_lod_example_standalone.py",
        "simple_tileset_lod_example_standalone.py", 
        "lod_scheduler.py",
        "tileset_data/florenz_village/tileset_simple.json"
    ]
    
    missing_files = []
    for file_path in expected_files:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"Missing files: {missing_files}")
        return False
    
    print("✅ All expected files present")
    return True

def test_simple_lod_functions_removed():
    """测试simple_lod_example_standalone.py中八叉树函数是否已移除"""
    print("\n=== Testing Simple LOD Functions Removal ===")
    
    file_path = os.path.join(current_dir, "simple_lod_example_standalone.py")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还包含八叉树相关函数
        removed_functions = [
            "start_octree_lod",
            "demo_octree_compatibility", 
            "start_octree_lod_switching_with_config"
        ]
        
        for func_name in removed_functions:
            if f"def {func_name}" in content:
                print(f"❌ Function {func_name} should have been removed")
                return False
            else:
                print(f"✅ Function {func_name} correctly removed")
        
        # 检查是否还包含八叉树相关的使用说明
        if "start_octree_lod()" in content:
            print("❌ start_octree_lod() still mentioned in usage instructions")
            return False
        else:
            print("✅ Octree references removed from usage instructions")
        
        # 检查保留的函数
        preserved_functions = [
            "start_lod",
            "stop_lod", 
            "manual_update",
            "start_automatic_lod_switching_with_config"
        ]
        
        for func_name in preserved_functions:
            if f"def {func_name}" in content:
                print(f"✅ Function {func_name} correctly preserved")
            else:
                print(f"❌ Function {func_name} should have been preserved")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing simple LOD functions: {e}")
        return False

def test_lod_scheduler_deprecation():
    """测试lod_scheduler.py中的废弃功能"""
    print("\n=== Testing LOD Scheduler Deprecation ===")
    
    file_path = os.path.join(current_dir, "lod_scheduler.py")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查build_octree_from_stage是否被废弃
        if "def build_octree_from_stage" in content:
            if "deprecated" in content or "废弃" in content:
                print("✅ build_octree_from_stage marked as deprecated")
            else:
                print("❌ build_octree_from_stage should be marked as deprecated")
                return False
        else:
            print("❌ build_octree_from_stage function missing")
            return False
        
        # 检查tileset方法是否保留
        if "def build_octree_from_tileset" in content:
            print("✅ build_octree_from_tileset method preserved")
        else:
            print("❌ build_octree_from_tileset method missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing LOD scheduler deprecation: {e}")
        return False

def test_tileset_standalone_structure():
    """测试tileset standalone文件结构"""
    print("\n=== Testing Tileset Standalone Structure ===")
    
    file_path = os.path.join(current_dir, "simple_tileset_lod_example_standalone.py")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和函数
        required_elements = [
            "class TilesetConfig",
            "def load_tileset_file",
            "def create_stage_and_load_tileset_prims",
            "def parse_tileset_bounding_box",
            "def start_automatic_tileset_lod_switching",
            "def main()"
        ]
        
        for element in required_elements:
            if element in content:
                print(f"✅ {element} found")
            else:
                print(f"❌ {element} missing")
                return False
        
        # 检查是否修复了attach_stage问题
        if "attach_stage" in content:
            # 应该有错误处理或替代方法
            if "hasattr(context, 'attach_stage')" in content or "try:" in content:
                print("✅ attach_stage issue handled with fallback")
            else:
                print("⚠️ attach_stage still used without error handling")
        else:
            print("✅ attach_stage completely replaced")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing tileset standalone structure: {e}")
        return False

def test_configuration_separation():
    """测试配置分离"""
    print("\n=== Testing Configuration Separation ===")
    
    try:
        # 检查simple_lod_example_standalone.py的配置
        simple_file = os.path.join(current_dir, "simple_lod_example_standalone.py")
        with open(simple_file, 'r', encoding='utf-8') as f:
            simple_content = f.read()
        
        # 检查tileset standalone的配置
        tileset_file = os.path.join(current_dir, "simple_tileset_lod_example_standalone.py")
        with open(tileset_file, 'r', encoding='utf-8') as f:
            tileset_content = f.read()
        
        # simple_lod应该有USDZ路径配置
        if "usdz_paths" in simple_content:
            print("✅ Simple LOD has USDZ paths configuration")
        else:
            print("❌ Simple LOD missing USDZ paths configuration")
            return False
        
        # tileset应该有tileset路径配置
        if "tileset_path" in tileset_content:
            print("✅ Tileset LOD has tileset path configuration")
        else:
            print("❌ Tileset LOD missing tileset path configuration")
            return False
        
        # 检查功能分离
        if "tileset" not in simple_content.lower() or simple_content.lower().count("tileset") < 3:
            print("✅ Simple LOD properly separated from tileset functionality")
        else:
            print("⚠️ Simple LOD may still contain tileset references")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration separation: {e}")
        return False

def test_sse_configuration():
    """测试SSE配置"""
    print("\n=== Testing SSE Configuration ===")
    
    try:
        # 检查tileset文件中的SSE配置
        tileset_file = os.path.join(current_dir, "simple_tileset_lod_example_standalone.py")
        with open(tileset_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查SSE配置是否更新为32.0
        if "maximum_screen_space_error = 32.0" in content:
            print("✅ SSE threshold updated to 32.0")
        elif "maximum_screen_space_error = 16.0" in content:
            print("⚠️ SSE threshold still at 16.0 (may be intentional)")
        else:
            print("❌ SSE threshold configuration not found")
            return False
        
        # 检查其他SSE相关配置
        sse_configs = [
            "lod_geometric_errors",
            "screen_width",
            "horizontal_fov"
        ]
        
        for config in sse_configs:
            if config in content:
                print(f"✅ {config} configuration found")
            else:
                print(f"❌ {config} configuration missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing SSE configuration: {e}")
        return False

def main():
    """主测试函数"""
    print("Code Structure Test (No Isaac Sim Dependencies)")
    print("=" * 60)
    
    tests = [
        test_file_structure,
        test_simple_lod_functions_removed,
        test_lod_scheduler_deprecation,
        test_tileset_standalone_structure,
        test_configuration_separation,
        test_sse_configuration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All code structure tests passed!")
        print("\nKey Changes Verified:")
        print("✅ Octree functions removed from simple_lod_example_standalone.py")
        print("✅ build_octree_from_stage deprecated in lod_scheduler.py")
        print("✅ Tileset functionality properly separated")
        print("✅ attach_stage issue handled in tileset standalone")
        print("✅ SSE configuration properly set")
        print("✅ Configuration separation maintained")
        
        print("\nSystem Architecture:")
        print("📁 simple_lod_example_standalone.py - Single-region LOD management")
        print("📁 simple_tileset_lod_example_standalone.py - Tileset-based LOD management")
        print("📁 lod_scheduler.py - Core LOD scheduling (tileset-focused)")
        
        print("\nUsage:")
        print("  Single-region: python simple_lod_example_standalone.py")
        print("  Tileset: python simple_tileset_lod_example_standalone.py")
        
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
