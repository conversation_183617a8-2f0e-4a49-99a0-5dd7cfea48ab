# Simple Tileset LOD Example - Standalone版本

## 概述

`simple_tileset_lod_example_standalone.py` 是一个专门的standalone应用程序，用于演示基于 `tileset.json` 文件的LOD管理系统。它支持直接运行Isaac Sim SimulationApp，自动创建stage，加载tileset中定义的USDZ文件，并实现基于SSE（Screen Space Error）的智能LOD切换。

## 主要特性

### 1. Standalone应用程序
- **独立运行**: 无需外部stage，自动创建和配置
- **SimulationApp集成**: 直接启动Isaac Sim standalone模式
- **自动初始化**: 自动等待渲染系统初始化完成
- **运行时管理**: 自动启动播放模式

### 2. Tileset.json支持
- **3D Tiles标准**: 完全兼容3D Tiles 1.1规范
- **层次结构解析**: 自动解析tileset的层次结构
- **USDZ文件加载**: 自动加载tileset中引用的USDZ文件
- **边界框解析**: 支持box、sphere、region三种边界体积格式

### 3. 智能LOD管理
- **SSE计算**: 基于几何误差和相机距离的屏幕空间误差计算
- **自动切换**: 实时根据相机位置自动切换LOD级别
- **可见性控制**: 智能控制不同LOD级别的可见性
- **性能优化**: 优先选择满足质量要求的最低LOD

## 文件结构

```
lod/
├── simple_tileset_lod_example_standalone.py  # 主程序
├── lod_scheduler.py                           # LOD调度器
├── tileset_data/
│   └── florenz_village/
│       ├── tileset_simple.json              # Tileset配置文件
│       ├── 16.usdz                          # 低质量LOD
│       ├── 18.usdz                          # 中质量LOD
│       └── 20.usdz                          # 高质量LOD
└── test_tileset_standalone.py               # 测试脚本
```

## 配置说明

### TilesetConfig类
```python
class TilesetConfig:
    def __init__(self):
        # Tileset文件配置
        self.tileset_path = "tileset_data/florenz_village/tileset_simple.json"
        
        # 相机配置
        self.camera_path = "/World/Camera"
        
        # LOD切换配置
        self.auto_mode = "movement"
        self.timer_interval = 1.0
        self.debug_info = True
        
        # 运行时控制
        self.auto_start_runtime = True
        
        # 几何误差配置（世界单位，米）
        self.lod_geometric_errors = {
            "High": 1.0,    # 高质量LOD：1.0米几何误差
            "Medium": 4.0,  # 中质量LOD：4.0米几何误差
            "Low": 8.0,     # 低质量LOD：8.0米几何误差
        }

        # SSE配置
        self.maximum_screen_space_error = 16.0  # 最大可接受屏幕误差阈值
        self.screen_width = 1920  # 视口宽度（像素）
        self.horizontal_fov = 60.0  # 水平视野角（度）
```

## 使用方法

### 1. 直接运行
```bash
python simple_tileset_lod_example_standalone.py
```

### 2. 程序化调用
```python
# 导入模块
from simple_tileset_lod_example_standalone import start_tileset_lod, stop_tileset_lod

# 启动tileset LOD
stage, region_bounds, scheduler = start_tileset_lod()

# 停止tileset LOD
stop_tileset_lod()
```

### 3. 自定义配置
```python
from simple_tileset_lod_example_standalone import TilesetConfig, run_tileset_standalone_mode

# 创建自定义配置
config = TilesetConfig()
config.tileset_path = "path/to/your/tileset.json"
config.maximum_screen_space_error = 8.0  # 更严格的质量要求

# 运行
result = run_tileset_standalone_mode(config)
```

## Tileset.json结构

### 示例结构
```json
{
    "asset": {
        "version": "1.1",
        "gltfUpAxis": "Z"
    },
    "geometricError": 8,
    "root": {
        "boundingVolume": {
            "box": [
                -144.29375839233398, -169.10734176635742, 5.648289680480957,
                79.42087173461914, 0, 0,
                0, 50.80354690551758, 0,
                0, 0, 13.114527702331543
            ]
        },
        "geometricError": 8,
        "refine": "REPLACE",
        "content": {
            "uri": "16.usdz"
        },
        "children": [
            {
                "boundingVolume": { "box": [...] },
                "geometricError": 4,
                "content": { "uri": "18.usdz" },
                "children": [
                    {
                        "boundingVolume": { "box": [...] },
                        "geometricError": 1,
                        "content": { "uri": "20.usdz" }
                    }
                ]
            }
        ]
    }
}
```

### LOD级别映射
- **geometricError ≤ 1.5**: High LOD (20.usdz)
- **1.5 < geometricError ≤ 5.0**: Medium LOD (18.usdz)
- **geometricError > 5.0**: Low LOD (16.usdz)

## 核心功能

### 1. Stage创建和USDZ加载
```python
def create_stage_and_load_tileset_prims(tileset_data, config):
    """创建stage并根据tileset数据加载USDZ prims"""
    # 创建新的stage
    stage = Usd.Stage.CreateNew("TilesetLODExample.usda")
    
    # 解析tileset层次结构并创建prims
    # 加载USDZ文件到对应的LOD级别
    # 设置初始可见性
```

### 2. LOD可见性更新
```python
def update_tileset_lod_visibility(stage, region_bounds, scheduler, verbose=True):
    """使用调度器更新tileset LOD可见性"""
    # 使用调度器的SSE方法更新LOD
    selected_lod, center_distance, representative_sse = scheduler.update_lod_visibility_by_sse(region_bounds, verbose)
    
    # 更新所有LOD级别的可见性
    # 只有选中的LOD可见，其他隐藏
```

### 3. 自动更新机制
```python
def _start_mainthread_update_subscription(stage, region_bounds, scheduler, config, update_interval=1.0):
    """使用Kit的update事件在主线程上进行节流更新"""
    # 订阅主线程更新事件
    # 定期检查相机位置变化
    # 自动切换LOD级别
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_tileset_standalone.py
```

测试内容包括：
- ✅ TilesetConfig配置测试
- ✅ Tileset.json文件加载测试
- ✅ 边界框解析逻辑测试
- ✅ LOD层次结构创建逻辑测试

## 与其他版本的区别

### vs simple_lod_example_standalone.py
- **专门化**: 专门针对tileset.json文件
- **独立性**: 完全独立的standalone应用
- **自动化**: 自动创建stage和加载资源

### vs lod_scheduler.py
- **应用层**: 提供完整的应用程序框架
- **用户友好**: 简化的配置和使用方式
- **演示性**: 专门用于演示tileset功能

## 优势特点

1. **开箱即用**: 无需额外配置，直接运行即可
2. **标准兼容**: 完全兼容3D Tiles标准
3. **智能优化**: 基于SSE的智能LOD选择
4. **实时响应**: 相机移动时实时更新LOD
5. **易于扩展**: 可轻松添加新的LOD级别或内容

## 使用场景

1. **快速原型**: 快速验证tileset LOD功能
2. **演示展示**: 向客户或团队展示LOD效果
3. **测试验证**: 测试不同的tileset配置
4. **学习研究**: 学习3D Tiles和LOD管理技术

这个standalone版本为tileset LOD管理提供了一个完整、易用的解决方案，特别适合需要快速部署和演示的场景。
