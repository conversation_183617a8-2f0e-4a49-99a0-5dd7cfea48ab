# SSE LOD Integration - 基于几何误差和相机距离的LOD调度

## 概述

本项目将 `simple_lod_example_standalone.py` 中的SSE（Screen Space Error）和距离计算逻辑集成到 `lod_scheduler.py` 中，实现了基于几何误差和相机距离的智能LOD选择，同时保持了八叉树瓦片结构的兼容性。

## 主要特性

### 1. SSE计算和LOD选择
- **几何误差配置**: 每个LOD级别都有对应的几何误差值（世界单位，米）
- **SSE公式**: `SSE = (geometricError × screenWidth) / (2 × distanceToCamera × tan(hFOV/2))`
- **距离范围映射**: 基于SSE阈值自动计算每个LOD的适用距离范围
- **智能选择策略**: 优先选择能满足SSE阈值的最低质量LOD（性能优化）

### 2. 八叉树结构兼容性
- **保留原有八叉树**: 完全兼容现有的八叉树瓦片结构
- **双重更新方法**: 支持传统方法和新的SSE方法
- **递归节点处理**: 在八叉树节点中应用SSE计算
- **细分策略**: 基于SSE结果决定是否继续细分节点

### 3. 单区域场景支持
- **直接LOD切换**: 支持单个区域的直接LOD级别切换
- **实时更新**: 相机移动时自动更新LOD可见性
- **配置灵活性**: 可自定义几何误差、SSE阈值等参数

## 文件修改说明

### `lod_scheduler.py` 的主要修改

#### 新增配置参数
```python
# 几何误差配置（世界单位，米）
self.lod_geometric_errors = {
    "High": 1.0,    # 高质量LOD：1.0米几何误差
    "Medium": 4.0,  # 中质量LOD：4.0米几何误差
    "Low": 8.0,     # 低质量LOD：8.0米几何误差
    "VeryLow": 16.0 # 极低质量LOD：16.0米几何误差
}

# SSE配置
self.maximum_screen_space_error = 16.0  # 最大可接受屏幕像素误差阈值
self.screen_width = 1920  # 视口宽度（像素）
self.horizontal_fov = 60.0  # 水平视野角（度）
```

#### 新增核心方法
1. **`calculate_sse()`**: 计算屏幕空间误差
2. **`calculate_distance_to_bounding_sphere()`**: 计算相机到包围球的合理距离
3. **`calculate_lod_distance_ranges()`**: 计算每个LOD对应的距离范围
4. **`select_lod_by_sse_and_distance()`**: 基于SSE和距离选择LOD
5. **`update_lod_visibility_by_sse()`**: 使用SSE策略更新LOD可见性
6. **`_update_node_visibility_sse()`**: 使用SSE方法递归更新八叉树节点可见性

### `simple_lod_example_standalone.py` 的主要修改

#### 集成外部调度器
```python
def update_lod_visibility_by_sse(stage, region_bounds, usdz_paths, verbose=True, config=None):
    """使用SSE策略确定LOD级别 - 现在使用LODScheduler"""
    try:
        # 尝试使用外部LODScheduler
        scheduler = LODScheduler(stage, camera_path="/World/Camera")
        
        # 配置调度器参数
        if config:
            if hasattr(config, 'lod_geometric_errors'):
                scheduler.lod_geometric_errors = config.lod_geometric_errors
            # ... 其他配置
        
        # 使用调度器的SSE方法
        return scheduler.update_lod_visibility_by_sse(region_bounds, verbose)
        
    except Exception as e:
        # 回退到内置方法
        return _update_lod_visibility_by_sse_builtin(...)
```

#### 新增八叉树支持函数
```python
def start_octree_lod_switching_with_config(config):
    """使用八叉树结构启动自动LOD切换"""
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=100.0)
    scheduler.update_lod_visibility(use_sse_method=True)
```

## 使用方法

### 1. 基本使用（单区域场景）
```python
# 启动单区域LOD切换
start_lod()

# 或者使用配置启动
config = StandaloneConfig()
start_automatic_lod_switching_with_config(config)
```

### 2. 八叉树场景使用
```python
# 启动八叉树LOD切换
start_octree_lod()

# 或者演示八叉树兼容性
demo_octree_compatibility()
```

### 3. 手动控制
```python
# 手动更新LOD
manual_update()

# 检查当前状态
check_status()

# 停止自动更新
stop_lod()
```

### 4. 高级配置
```python
config = StandaloneConfig()

# 自定义几何误差
config.lod_geometric_errors = {
    "High": 0.5,    # 更高质量
    "Medium": 2.0,
    "Low": 6.0
}

# 自定义SSE阈值
config.maximum_screen_space_error = 8.0  # 更严格的质量要求

# 自定义屏幕参数
config.screen_width = 2560
config.horizontal_fov = 75.0
```

## LOD选择策略

### 距离范围计算
基于SSE阈值，系统自动计算每个LOD的适用距离范围：

1. **High LOD**: 0 到 Medium阈值距离
2. **Medium LOD**: Medium阈值距离 到 Low阈值距离  
3. **Low LOD**: Low阈值距离 到 无穷远

### 选择逻辑
- 优先选择能满足SSE阈值的最低质量LOD（性能优化）
- 近距离优先使用高质量LOD（质量优先）
- 远距离自动降级到低质量LOD（性能优先）

## 兼容性说明

### 向后兼容
- 保留所有原有的传统LOD方法
- 支持原有的距离阈值配置
- 八叉树结构完全兼容

### 渐进式升级
- 可以选择使用新的SSE方法或传统方法
- 支持混合使用两种方法
- 提供回退机制确保稳定性

## 测试验证

运行测试脚本验证集成功能：
```bash
python test_sse_lod_integration.py
```

测试内容包括：
- LOD调度器导入测试
- SSE计算功能测试
- LOD选择逻辑测试
- 与standalone脚本集成测试

## 总结

本次修改成功将先进的SSE计算逻辑集成到LOD调度器中，实现了：

1. **智能LOD选择**: 基于几何误差和屏幕误差的科学选择
2. **八叉树兼容**: 完全支持现有八叉树瓦片结构
3. **性能优化**: 自动选择最适合的LOD级别
4. **灵活配置**: 支持多种参数自定义
5. **向后兼容**: 保留所有原有功能

这使得系统既能处理单一区域的简单场景，也能处理复杂的八叉树瓦片场景，为不同应用需求提供了灵活的解决方案。
