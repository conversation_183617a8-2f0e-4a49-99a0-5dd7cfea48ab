"""
LOD调度系统包
基于屏幕误差和相机距离的LOD调度系统，类似于Cesium 3D Tiles的实现
"""

from .lod_scheduler import (
    LODScheduler,
    BoundingBox,
    LODTile,
    OctreeNode,
    LODLevel
)

from .lod_config import (
    LODSystemConfig,
    LODConfig,
    OctreeConfig,
    CameraConfig,
    PerformanceConfig,
    DEFAULT_CONFIG,
    HIGH_QUALITY_CONFIG,
    PERFORMANCE_CONFIG,
    create_custom_config
)

__version__ = "1.0.0"
__author__ = "LOD System Developer"

__all__ = [
    # 主要类
    "LODScheduler",
    "BoundingBox", 
    "LODTile",
    "OctreeNode",
    "LODLevel",
    
    # 配置类
    "LODSystemConfig",
    "LODConfig",
    "OctreeConfig", 
    "CameraConfig",
    "PerformanceConfig",
    
    # 预设配置
    "DEFAULT_CONFIG",
    "HIGH_QUALITY_CONFIG", 
    "PERFORMANCE_CONFIG",
    
    # 工具函数
    "create_custom_config"
] 