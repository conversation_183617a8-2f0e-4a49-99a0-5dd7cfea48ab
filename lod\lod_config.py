"""
LOD配置文件
用于管理LOD调度系统的各种参数和配置
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any
import json

class LODLevel(Enum):
    """LOD级别枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    VERY_LOW = "very_low"

@dataclass
class LODConfig:
    """LOD配置类"""
    screen_error: float  # 屏幕误差阈值（像素）
    distance_threshold: float  # 距离阈值（单位）
    geometric_error: float = 0.0  # 几何误差（可选）
    max_triangles: int = 10000  # 最大三角形数量（可选）

@dataclass
class OctreeConfig:
    """八叉树配置类"""
    max_depth: int = 8  # 最大深度
    min_area_threshold: float = 100.0  # 最小面积阈值
    min_volume_threshold: float = 1000.0  # 最小体积阈值

@dataclass
class CameraConfig:
    """相机配置类"""
    fov: float = 60.0  # 视场角（度）
    screen_width: int = 1920  # 屏幕宽度
    screen_height: int = 1080  # 屏幕高度
    near_plane: float = 0.1  # 近平面
    far_plane: float = 1000.0  # 远平面

@dataclass
class PerformanceConfig:
    """性能配置类"""
    max_tiles_per_frame: int = 1000  # 每帧最大瓦片数量
    update_interval: float = 0.1  # 更新间隔（秒）
    enable_frustum_culling: bool = True  # 启用视锥体剔除
    enable_occlusion_culling: bool = False  # 启用遮挡剔除

class LODSystemConfig:
    """LOD系统配置管理器"""
    
    def __init__(self):
        # 默认LOD配置
        self.lod_configs = {
            LODLevel.HIGH: LODConfig(
                screen_error=1.0,
                distance_threshold=50.0,
                geometric_error=0.1,
                max_triangles=5000
            ),
            LODLevel.MEDIUM: LODConfig(
                screen_error=5.0,
                distance_threshold=100.0,
                geometric_error=0.5,
                max_triangles=2000
            ),
            LODLevel.LOW: LODConfig(
                screen_error=15.0,
                distance_threshold=200.0,
                geometric_error=2.0,
                max_triangles=500
            ),
            LODLevel.VERY_LOW: LODConfig(
                screen_error=50.0,
                distance_threshold=500.0,
                geometric_error=10.0,
                max_triangles=100
            )
        }
        
        # 八叉树配置
        self.octree_config = OctreeConfig()
        
        # 相机配置
        self.camera_config = CameraConfig()
        
        # 性能配置
        self.performance_config = PerformanceConfig()
        
        # 其他配置
        self.coordinate_system_correction = True  # 是否应用坐标系修正
        self.debug_mode = False  # 调试模式
        self.log_level = "INFO"  # 日志级别
    
    def get_lod_config(self, level: LODLevel) -> LODConfig:
        """获取指定级别的LOD配置"""
        return self.lod_configs.get(level)
    
    def set_lod_config(self, level: LODLevel, config: LODConfig):
        """设置指定级别的LOD配置"""
        self.lod_configs[level] = config
    
    def get_optimal_lod_level(self, screen_error: float, distance: float) -> LODLevel:
        """根据屏幕误差和距离确定最优LOD级别"""
        for level in [LODLevel.HIGH, LODLevel.MEDIUM, LODLevel.LOW, LODLevel.VERY_LOW]:
            config = self.lod_configs[level]
            if screen_error <= config.screen_error and distance <= config.distance_threshold:
                return level
        return LODLevel.VERY_LOW
    
    def should_subdivide(self, screen_error: float, area: float, depth: int) -> bool:
        """判断是否应该细分"""
        return (depth < self.octree_config.max_depth and
                area > self.octree_config.min_area_threshold and
                screen_error > self.lod_configs[LODLevel.HIGH].screen_error)
    
    def save_config(self, file_path: str):
        """保存配置到文件"""
        config_data = {
            "lod_configs": {
                level.value: {
                    "screen_error": config.screen_error,
                    "distance_threshold": config.distance_threshold,
                    "geometric_error": config.geometric_error,
                    "max_triangles": config.max_triangles
                }
                for level, config in self.lod_configs.items()
            },
            "octree_config": {
                "max_depth": self.octree_config.max_depth,
                "min_area_threshold": self.octree_config.min_area_threshold,
                "min_volume_threshold": self.octree_config.min_volume_threshold
            },
            "camera_config": {
                "fov": self.camera_config.fov,
                "screen_width": self.camera_config.screen_width,
                "screen_height": self.camera_config.screen_height,
                "near_plane": self.camera_config.near_plane,
                "far_plane": self.camera_config.far_plane
            },
            "performance_config": {
                "max_tiles_per_frame": self.performance_config.max_tiles_per_frame,
                "update_interval": self.performance_config.update_interval,
                "enable_frustum_culling": self.performance_config.enable_frustum_culling,
                "enable_occlusion_culling": self.performance_config.enable_occlusion_culling
            },
            "other_config": {
                "coordinate_system_correction": self.coordinate_system_correction,
                "debug_mode": self.debug_mode,
                "log_level": self.log_level
            }
        }
        
        with open(file_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"Configuration saved to {file_path}")
    
    def load_config(self, file_path: str):
        """从文件加载配置"""
        try:
            with open(file_path, 'r') as f:
                config_data = json.load(f)
            
            # 加载LOD配置
            for level_str, config_dict in config_data["lod_configs"].items():
                level = LODLevel(level_str)
                config = LODConfig(
                    screen_error=config_dict["screen_error"],
                    distance_threshold=config_dict["distance_threshold"],
                    geometric_error=config_dict.get("geometric_error", 0.0),
                    max_triangles=config_dict.get("max_triangles", 10000)
                )
                self.lod_configs[level] = config
            
            # 加载八叉树配置
            octree_dict = config_data["octree_config"]
            self.octree_config = OctreeConfig(
                max_depth=octree_dict["max_depth"],
                min_area_threshold=octree_dict["min_area_threshold"],
                min_volume_threshold=octree_dict["min_volume_threshold"]
            )
            
            # 加载相机配置
            camera_dict = config_data["camera_config"]
            self.camera_config = CameraConfig(
                fov=camera_dict["fov"],
                screen_width=camera_dict["screen_width"],
                screen_height=camera_dict["screen_height"],
                near_plane=camera_dict["near_plane"],
                far_plane=camera_dict["far_plane"]
            )
            
            # 加载性能配置
            perf_dict = config_data["performance_config"]
            self.performance_config = PerformanceConfig(
                max_tiles_per_frame=perf_dict["max_tiles_per_frame"],
                update_interval=perf_dict["update_interval"],
                enable_frustum_culling=perf_dict["enable_frustum_culling"],
                enable_occlusion_culling=perf_dict["enable_occlusion_culling"]
            )
            
            # 加载其他配置
            other_dict = config_data["other_config"]
            self.coordinate_system_correction = other_dict["coordinate_system_correction"]
            self.debug_mode = other_dict["debug_mode"]
            self.log_level = other_dict["log_level"]
            
            print(f"Configuration loaded from {file_path}")
            
        except Exception as e:
            print(f"ERROR: Failed to load configuration from {file_path}: {e}")
    
    def get_default_config() -> 'LODSystemConfig':
        """获取默认配置"""
        return LODSystemConfig()
    
    def get_high_quality_config() -> 'LODSystemConfig':
        """获取高质量配置"""
        config = LODSystemConfig()
        
        # 更严格的LOD配置
        config.lod_configs[LODLevel.HIGH] = LODConfig(0.5, 25.0, 0.05, 10000)
        config.lod_configs[LODLevel.MEDIUM] = LODConfig(2.0, 75.0, 0.2, 5000)
        config.lod_configs[LODLevel.LOW] = LODConfig(8.0, 150.0, 1.0, 2000)
        config.lod_configs[LODLevel.VERY_LOW] = LODConfig(25.0, 300.0, 5.0, 500)
        
        # 更深的八叉树
        config.octree_config.max_depth = 10
        config.octree_config.min_area_threshold = 25.0
        
        return config
    
    def get_performance_config() -> 'LODSystemConfig':
        """获取性能优化配置"""
        config = LODSystemConfig()
        
        # 更宽松的LOD配置
        config.lod_configs[LODLevel.HIGH] = LODConfig(2.0, 100.0, 0.5, 2000)
        config.lod_configs[LODLevel.MEDIUM] = LODConfig(10.0, 200.0, 2.0, 1000)
        config.lod_configs[LODLevel.LOW] = LODConfig(25.0, 400.0, 5.0, 500)
        config.lod_configs[LODLevel.VERY_LOW] = LODConfig(100.0, 1000.0, 20.0, 100)
        
        # 更浅的八叉树
        config.octree_config.max_depth = 6
        config.octree_config.min_area_threshold = 200.0
        
        # 性能优化
        config.performance_config.max_tiles_per_frame = 500
        config.performance_config.update_interval = 0.2
        
        return config

# 预定义配置
DEFAULT_CONFIG = LODSystemConfig.get_default_config()
HIGH_QUALITY_CONFIG = LODSystemConfig.get_high_quality_config()
PERFORMANCE_CONFIG = LODSystemConfig.get_performance_config()

def create_custom_config(
    screen_error_thresholds: Dict[str, float] = None,
    distance_thresholds: Dict[str, float] = None,
    max_depth: int = 8,
    min_area_threshold: float = 100.0
) -> LODSystemConfig:
    """创建自定义配置"""
    config = LODSystemConfig()
    
    if screen_error_thresholds:
        for level_str, threshold in screen_error_thresholds.items():
            level = LODLevel(level_str)
            if level in config.lod_configs:
                config.lod_configs[level].screen_error = threshold
    
    if distance_thresholds:
        for level_str, threshold in distance_thresholds.items():
            level = LODLevel(level_str)
            if level in config.lod_configs:
                config.lod_configs[level].distance_threshold = threshold
    
    config.octree_config.max_depth = max_depth
    config.octree_config.min_area_threshold = min_area_threshold
    
    return config 