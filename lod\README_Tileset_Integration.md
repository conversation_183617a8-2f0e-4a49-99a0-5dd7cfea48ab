# Tileset.json 八叉树集成

## 概述

现在 `lod_scheduler.py` 支持从 `tileset.json` 文件构建八叉树结构，这使得系统可以直接使用3D Tiles标准的tileset文件来管理LOD层次结构。

## 功能特性

### 1. Tileset.json 支持
- **3D Tiles标准**: 完全兼容3D Tiles 1.1规范
- **多种边界框格式**: 支持box、sphere、region三种边界体积格式
- **层次结构**: 自动解析tileset的层次结构并映射到八叉树
- **几何误差映射**: 根据tileset中的geometricError自动确定LOD级别

### 2. 边界框解析
支持3D Tiles标准的三种边界体积格式：

#### Box格式
```json
"boundingVolume": {
    "box": [
        center_x, center_y, center_z,
        x_axis_x, x_axis_y, x_axis_z,
        y_axis_x, y_axis_y, y_axis_z,
        z_axis_x, z_axis_y, z_axis_z
    ]
}
```

#### Sphere格式
```json
"boundingVolume": {
    "sphere": [center_x, center_y, center_z, radius]
}
```

#### Region格式
```json
"boundingVolume": {
    "region": [west, south, east, north, minimum_height, maximum_height]
}
```

### 3. LOD级别映射
根据tileset中的`geometricError`自动映射LOD级别：
- **geometricError ≤ 1.5**: High LOD
- **1.5 < geometricError ≤ 5.0**: Medium LOD
- **geometricError > 5.0**: Low LOD

## 使用方法

### 1. 基本使用
```python
# 使用默认tileset文件
start_tileset_lod()

# 使用自定义tileset文件
start_tileset_lod("path/to/your/tileset.json")
```

### 2. 高级配置
```python
# 创建配置
config = StandaloneConfig()

# 自定义SSE参数
config.maximum_screen_space_error = 8.0
config.screen_width = 2560

# 使用tileset启动
tileset_path = "tileset_data/florenz_village/tileset_simple.json"
stage, bounds, scheduler = start_tileset_lod_switching_with_config(config, tileset_path)
```

### 3. 直接使用调度器
```python
# 创建调度器
stage = omni.usd.get_context().get_stage()
scheduler = LODScheduler(stage)

# 从tileset构建八叉树
scheduler.build_octree_from_tileset("path/to/tileset.json")

# 更新LOD可见性
scheduler.update_lod_visibility(use_sse_method=True)
```

## Tileset文件结构示例

### 简单的三级LOD结构
```json
{
    "asset": {
        "version": "1.1",
        "gltfUpAxis": "Z"
    },
    "geometricError": 8,
    "root": {
        "boundingVolume": {
            "box": [...]
        },
        "geometricError": 8,
        "refine": "REPLACE",
        "content": {
            "uri": "16.usdz"
        },
        "children": [
            {
                "boundingVolume": {
                    "box": [...]
                },
                "geometricError": 4,
                "refine": "REPLACE",
                "content": {
                    "uri": "18.usdz"
                },
                "children": [
                    {
                        "boundingVolume": {
                            "box": [...]
                        },
                        "geometricError": 1,
                        "refine": "REPLACE",
                        "content": {
                            "uri": "20.usdz"
                        }
                    }
                ]
            }
        ]
    }
}
```

## 新增的API方法

### LODScheduler类新增方法

#### `build_octree_from_tileset(tileset_path, base_path=None)`
从tileset.json文件构建八叉树
- `tileset_path`: tileset.json文件路径
- `base_path`: 资源文件的基础路径（可选）

#### `_parse_tileset_bounding_box(bounding_volume)`
解析tileset中的边界框
- 支持box、sphere、region三种格式
- 返回BoundingBox对象

#### `_build_tileset_hierarchy(tile_data, octree_node, base_path, depth=0)`
递归构建tileset层次结构
- 自动解析子瓦片
- 映射几何误差到LOD级别
- 构建完整的文件路径

### Simple LOD Example新增函数

#### `start_tileset_lod(tileset_path=None)`
便捷函数，启动基于tileset的LOD切换

#### `start_tileset_lod_switching_with_config(config, tileset_path)`
使用配置启动tileset LOD切换

## 与现有功能的兼容性

### 完全兼容
- ✅ 保留所有原有的八叉树功能
- ✅ 支持SSE计算和LOD选择
- ✅ 兼容单区域场景
- ✅ 支持传统的stage构建方法

### 选择性使用
```python
# 方法1: 从stage构建（原有方法）
scheduler.build_octree_from_stage()

# 方法2: 从tileset构建（新方法）
scheduler.build_octree_from_tileset("tileset.json")

# 两种方法都支持SSE更新
scheduler.update_lod_visibility(use_sse_method=True)
```

## 测试验证

运行测试脚本验证tileset集成功能：
```bash
python test_tileset_integration.py
```

测试内容包括：
- ✅ Tileset.json解析
- ✅ 边界框解析（box、sphere、region）
- ✅ LOD级别映射
- ✅ 层次结构分析

## 实际应用场景

### 1. 城市建模
使用tileset.json管理不同细节级别的城市模型：
- 远景：简化的城市轮廓
- 中景：建筑物外观
- 近景：详细的建筑结构

### 2. 地形渲染
基于地理数据的多级地形：
- 低分辨率：全球地形
- 中分辨率：区域地形
- 高分辨率：局部详细地形

### 3. 工业场景
复杂工业设施的LOD管理：
- 整体布局视图
- 设备组视图
- 单个设备详细视图

## 优势总结

1. **标准化**: 使用3D Tiles标准，与行业标准兼容
2. **灵活性**: 支持复杂的层次结构定义
3. **可扩展性**: 易于添加新的LOD级别和内容
4. **性能优化**: 基于几何误差的智能LOD选择
5. **易于维护**: 外部配置文件，便于修改和管理

这个集成使得LOD系统更加强大和灵活，既可以处理简单的单区域场景，也可以处理复杂的多层次瓦片场景，为不同的应用需求提供了统一的解决方案。
