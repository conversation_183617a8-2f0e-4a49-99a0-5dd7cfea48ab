"""
LOD调度系统使用示例
演示如何使用LOD调度系统来管理场景的LOD级别
"""

from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd
import math
import sys
sys.path.append("E:/wanleqi/isaacsim-python-scripts/lod")

from lod_scheduler import LODScheduler, BoundingBox, LODLevel, LODTile
from lod_config import LODSystemConfig, HIGH_QUALITY_CONFIG, PERFORMANCE_CONFIG

def create_custom_test_scene():
    """创建自定义测试场景 - 4个区域，每个区域3个LOD层级"""
    stage = omni.usd.get_context().get_stage()
    
    print("Creating custom test scene with 4 regions and 3 LOD levels each...")
    
    # 定义4个区域的配置
    regions = [
        {
            "name": "Region_North",
            "bounds": BoundingBox(Gf.Vec3f(-300, 200, -100), Gf.Vec3f(300, 500, 100)),
            "center": Gf.Vec3f(0, 350, 0),
            "size": 80.0,
            "description": "北方区域"
        },
        {
            "name": "Region_South", 
            "bounds": BoundingBox(Gf.Vec3f(-300, -500, -100), Gf.Vec3f(300, -200, 100)),
            "center": Gf.Vec3f(0, -350, 0),
            "size": 80.0,
            "description": "南方区域"
        },
        {
            "name": "Region_East",
            "bounds": BoundingBox(Gf.Vec3f(200, -300, -100), Gf.Vec3f(500, 300, 100)),
            "center": Gf.Vec3f(350, 0, 0),
            "size": 80.0,
            "description": "东方区域"
        },
        {
            "name": "Region_West",
            "bounds": BoundingBox(Gf.Vec3f(-500, -300, -100), Gf.Vec3f(-200, 300, 100)),
            "center": Gf.Vec3f(-350, 0, 0),
            "size": 80.0,
            "description": "西方区域"
        }
    ]
    
    # 为每个区域创建3个LOD层级
    for region in regions:
        region_path = f"/World/CustomRegions/{region['name']}"
        region_prim = stage.DefinePrim(region_path)
        region_prim.SetTypeName("Xform")
        
        # 创建3个LOD层级
        lod_levels = [
            {"level": "High", "size": region['size'], "color": (1.0, 0.0, 0.0)},      # 红色 - 高质量
            {"level": "Medium", "size": region['size'] * 0.7, "color": (0.0, 1.0, 0.0)},  # 绿色 - 中等质量
            {"level": "Low", "size": region['size'] * 0.4, "color": (0.0, 0.0, 1.0)}      # 蓝色 - 低质量
        ]
        
        for lod_info in lod_levels:
            # 创建LOD层级容器
            lod_path = f"{region_path}/LOD_{lod_info['level']}"
            lod_prim = stage.DefinePrim(lod_path)
            lod_prim.SetTypeName("Xform")
            
            # 创建几何体
            geom_path = f"{lod_path}/Geometry"
            cube = UsdGeom.Cube.Define(stage, geom_path)
            cube.CreateSizeAttr().Set(lod_info['size'])
            
            # 设置位置（稍微偏移以避免重叠）
            offset = 0
            if lod_info['level'] == "Medium":
                offset = 20
            elif lod_info['level'] == "Low":
                offset = 40
            
            cube.AddTranslateOp().Set(Gf.Vec3f(
                region['center'][0] + offset,
                region['center'][1] + offset,
                region['center'][2]
            ))
            
            # 设置边界属性（使用区域边界）
            bounds = region['bounds']
            lod_prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3).Set(bounds.min_point)
            lod_prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3).Set(bounds.max_point)
            
            # 添加LOD级别标识属性
            lod_prim.CreateAttribute("omni:nurec:lod:level", Sdf.ValueTypeNames.String).Set(lod_info['level'])
            
            print(f"Created {region['name']} - LOD_{lod_info['level']} at {lod_path}")
    
    # 确保相机存在
    camera_path = "/World/Camera"
    if not stage.GetPrimAtPath(camera_path):
        camera = UsdGeom.Camera.Define(stage, camera_path)
        camera.AddTranslateOp().Set(Gf.Vec3f(0, 0, 800))
        print(f"Created camera at {camera_path}")
    
    print("Custom test scene created successfully!")
    return stage

def create_custom_lod_tiles():
    """创建自定义LOD瓦片配置"""
    print("\n=== Creating Custom LOD Tiles ===")
    
    # 定义4个区域的LOD瓦片
    custom_tiles = []
    
    # 北方区域
    north_bounds = BoundingBox(Gf.Vec3f(-300, 200, -100), Gf.Vec3f(300, 500, 100))
    custom_tiles.extend([
        LODTile(
            id="/World/CustomRegions/Region_North/LOD_High",
            bounding_box=north_bounds,
            lod_level=LODLevel.HIGH,
            usdz_path="/path/to/north_high_lod.usdz",
            screen_error=1.0,
            distance_threshold=100.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_North/LOD_Medium",
            bounding_box=north_bounds,
            lod_level=LODLevel.MEDIUM,
            usdz_path="/path/to/north_medium_lod.usdz",
            screen_error=5.0,
            distance_threshold=200.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_North/LOD_Low",
            bounding_box=north_bounds,
            lod_level=LODLevel.LOW,
            usdz_path="/path/to/north_low_lod.usdz",
            screen_error=15.0,
            distance_threshold=400.0
        )
    ])
    
    # 南方区域
    south_bounds = BoundingBox(Gf.Vec3f(-300, -500, -100), Gf.Vec3f(300, -200, 100))
    custom_tiles.extend([
        LODTile(
            id="/World/CustomRegions/Region_South/LOD_High",
            bounding_box=south_bounds,
            lod_level=LODLevel.HIGH,
            usdz_path="/path/to/south_high_lod.usdz",
            screen_error=1.0,
            distance_threshold=100.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_South/LOD_Medium",
            bounding_box=south_bounds,
            lod_level=LODLevel.MEDIUM,
            usdz_path="/path/to/south_medium_lod.usdz",
            screen_error=5.0,
            distance_threshold=200.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_South/LOD_Low",
            bounding_box=south_bounds,
            lod_level=LODLevel.LOW,
            usdz_path="/path/to/south_low_lod.usdz",
            screen_error=15.0,
            distance_threshold=400.0
        )
    ])
    
    # 东方区域
    east_bounds = BoundingBox(Gf.Vec3f(200, -300, -100), Gf.Vec3f(500, 300, 100))
    custom_tiles.extend([
        LODTile(
            id="/World/CustomRegions/Region_East/LOD_High",
            bounding_box=east_bounds,
            lod_level=LODLevel.HIGH,
            usdz_path="/path/to/east_high_lod.usdz",
            screen_error=1.0,
            distance_threshold=100.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_East/LOD_Medium",
            bounding_box=east_bounds,
            lod_level=LODLevel.MEDIUM,
            usdz_path="/path/to/east_medium_lod.usdz",
            screen_error=5.0,
            distance_threshold=200.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_East/LOD_Low",
            bounding_box=east_bounds,
            lod_level=LODLevel.LOW,
            usdz_path="/path/to/east_low_lod.usdz",
            screen_error=15.0,
            distance_threshold=400.0
        )
    ])
    
    # 西方区域
    west_bounds = BoundingBox(Gf.Vec3f(-500, -300, -100), Gf.Vec3f(-200, 300, 100))
    custom_tiles.extend([
        LODTile(
            id="/World/CustomRegions/Region_West/LOD_High",
            bounding_box=west_bounds,
            lod_level=LODLevel.HIGH,
            usdz_path="/path/to/west_high_lod.usdz",
            screen_error=1.0,
            distance_threshold=100.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_West/LOD_Medium",
            bounding_box=west_bounds,
            lod_level=LODLevel.MEDIUM,
            usdz_path="/path/to/west_medium_lod.usdz",
            screen_error=5.0,
            distance_threshold=200.0
        ),
        LODTile(
            id="/World/CustomRegions/Region_West/LOD_Low",
            bounding_box=west_bounds,
            lod_level=LODLevel.LOW,
            usdz_path="/path/to/west_low_lod.usdz",
            screen_error=15.0,
            distance_threshold=400.0
        )
    ])
    
    return custom_tiles

def custom_scene_lod_example():
    """自定义场景LOD示例"""
    print("\n=== Custom Scene LOD Example ===")
    
    # 创建自定义测试场景
    stage = create_custom_test_scene()
    
    # 创建LOD调度器
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 创建自定义LOD瓦片
    custom_tiles = create_custom_lod_tiles()
    
    # 添加自定义瓦片到调度器
    for tile in custom_tiles:
        scheduler.lod_tiles[tile.id] = tile
        print(f"Added LOD tile: {tile.id}")
        print(f"  Bounds: {tile.bounding_box.min_point} to {tile.bounding_box.max_point}")
        print(f"  LOD Level: {tile.lod_level.value}")
        print(f"  Screen Error: {tile.screen_error}")
        print(f"  Distance Threshold: {tile.distance_threshold}")
    
    # 构建八叉树
    print("\nBuilding octree for custom scene...")
    scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=50.0)
    
    # 更新LOD可见性
    print("Updating LOD visibility...")
    scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
    
    print("Custom scene LOD example completed!")

def test_camera_positions():
    """测试不同相机位置的LOD切换"""
    print("\n=== Testing Camera Positions ===")
    
    stage = omni.usd.get_context().get_stage()
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 添加自定义LOD瓦片
    custom_tiles = create_custom_lod_tiles()
    for tile in custom_tiles:
        scheduler.lod_tiles[tile.id] = tile
    
    # 构建八叉树
    scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=50.0)
    
    # 测试不同的相机位置
    test_positions = [
        {"name": "Center Far", "pos": Gf.Vec3f(0, 0, 1000), "expected_lod": "Low"},
        {"name": "North Close", "pos": Gf.Vec3f(0, 400, 150), "expected_lod": "High"},
        {"name": "South Medium", "pos": Gf.Vec3f(0, -300, 300), "expected_lod": "Medium"},
        {"name": "East Close", "pos": Gf.Vec3f(450, 0, 150), "expected_lod": "High"},
        {"name": "West Medium", "pos": Gf.Vec3f(-400, 0, 300), "expected_lod": "Medium"},
        {"name": "Corner Far", "pos": Gf.Vec3f(600, 600, 800), "expected_lod": "Low"}
    ]
    
    for test in test_positions:
        print(f"\n--- Testing {test['name']} ---")
        print(f"Camera position: {test['pos']}")
        print(f"Expected LOD: {test['expected_lod']}")
        
        # 更新相机位置
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            translate_op = xformable.AddTranslateOp()
            translate_op.Set(test['pos'])
        
        # 更新LOD可见性
        scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
        
        # 获取当前相机位置
        current_pos = scheduler.get_camera_position()
        if current_pos:
            print(f"Current camera position: {current_pos}")

def performance_test_custom_scene():
    """自定义场景性能测试"""
    print("\n=== Performance Test for Custom Scene ===")
    
    import time
    
    stage = omni.usd.get_context().get_stage()
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 添加自定义LOD瓦片
    custom_tiles = create_custom_lod_tiles()
    for tile in custom_tiles:
        scheduler.lod_tiles[tile.id] = tile
    
    # 测试构建性能
    print("Testing octree build performance...")
    start_time = time.time()
    scheduler.build_octree_from_stage(max_depth=6, min_area_threshold=50.0)
    build_time = time.time() - start_time
    print(f"Octree build time: {build_time:.3f} seconds")
    
    # 测试更新性能
    print("Testing LOD update performance...")
    start_time = time.time()
    scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
    update_time = time.time() - start_time
    print(f"LOD update time: {update_time:.3f} seconds")
    
    print(f"Total LOD tiles: {len(scheduler.lod_tiles)}")
    print(f"Total regions: 4")
    print(f"LOD levels per region: 3")
    print("Performance test completed!")

def create_simple_scene():
    """创建一个简单的测试场景"""
    stage = omni.usd.get_context().get_stage()
    
    print("Creating simple test scene...")
    
    # 创建几个不同大小的区域
    regions = [
        {
            "name": "LargeRegion",
            "bounds": BoundingBox(Gf.Vec3f(-200, -200, -200), Gf.Vec3f(200, 200, 200)),
            "size": 100.0
        },
        {
            "name": "MediumRegion",
            "bounds": BoundingBox(Gf.Vec3f(300, -100, -100), Gf.Vec3f(500, 100, 100)),
            "size": 50.0
        },
        {
            "name": "SmallRegion",
            "bounds": BoundingBox(Gf.Vec3f(-300, 300, -50), Gf.Vec3f(-200, 400, 50)),
            "size": 25.0
        }
    ]
    
    for region in regions:
        # 创建区域容器
        region_path = f"/World/Regions/{region['name']}"
        region_prim = stage.DefinePrim(region_path)
        region_prim.SetTypeName("Xform")
        
        # 创建几何体
        geom_path = f"{region_path}/Geometry"
        cube = UsdGeom.Cube.Define(stage, geom_path)
        cube.CreateSizeAttr().Set(region['size'])
        
        # 设置边界属性
        bounds = region['bounds']
        region_prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3).Set(bounds.min_point)
        region_prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3).Set(bounds.max_point)
        
        print(f"Created {region['name']} at {region_path}")
    
    # 确保相机存在
    camera_path = "/World/Camera"
    if not stage.GetPrimAtPath(camera_path):
        camera = UsdGeom.Camera.Define(stage, camera_path)
        camera.AddTranslateOp().Set(Gf.Vec3f(0, 0, 500))
        print(f"Created camera at {camera_path}")
    
    print("Simple test scene created!")
    return stage

def basic_lod_example():
    """基本LOD使用示例"""
    print("\n=== Basic LOD Example ===")
    
    # 创建场景
    stage = create_simple_scene()
    
    # 创建LOD调度器
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 构建八叉树
    print("Building octree...")
    scheduler.build_octree_from_stage(max_depth=4, min_area_threshold=50.0)
    
    # 更新LOD可见性
    print("Updating LOD visibility...")
    scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
    
    print("Basic LOD example completed!")

def config_based_example():
    """基于配置的LOD示例"""
    print("\n=== Configuration-based LOD Example ===")
    
    stage = omni.usd.get_context().get_stage()
    
    # 使用高质量配置
    print("Using high quality configuration...")
    config = HIGH_QUALITY_CONFIG
    
    # 创建调度器
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 使用配置参数构建八叉树
    scheduler.build_octree_from_stage(
        max_depth=config.octree_config.max_depth,
        min_area_threshold=config.octree_config.min_area_threshold
    )
    
    # 使用配置参数更新可见性
    scheduler.update_lod_visibility(
        fov=config.camera_config.fov,
        screen_height=config.camera_config.screen_height
    )
    
    print("Configuration-based LOD example completed!")

def custom_lod_tiles_example():
    """自定义LOD瓦片示例"""
    print("\n=== Custom LOD Tiles Example ===")
    
    stage = omni.usd.get_context().get_stage()
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 创建自定义LOD瓦片
    custom_tiles = [
        LODTile(
            id="/World/CustomTile1",
            bounding_box=BoundingBox(Gf.Vec3f(-100, -100, -100), Gf.Vec3f(100, 100, 100)),
            lod_level=LODLevel.HIGH,
            usdz_path="/path/to/high_detail.usdz",
            screen_error=0.5,
            distance_threshold=75.0
        ),
        LODTile(
            id="/World/CustomTile2",
            bounding_box=BoundingBox(Gf.Vec3f(200, -50, -50), Gf.Vec3f(400, 50, 50)),
            lod_level=LODLevel.MEDIUM,
            usdz_path="/path/to/medium_detail.usdz",
            screen_error=2.0,
            distance_threshold=150.0
        )
    ]
    
    # 添加自定义瓦片
    for tile in custom_tiles:
        scheduler.lod_tiles[tile.id] = tile
        print(f"Added custom tile: {tile.id}")
    
    # 构建八叉树并更新
    scheduler.build_octree_from_stage(max_depth=5, min_area_threshold=75.0)
    scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
    
    print("Custom LOD tiles example completed!")

def performance_comparison():
    """性能对比示例"""
    print("\n=== Performance Comparison Example ===")
    
    import time
    
    stage = omni.usd.get_context().get_stage()
    
    # 测试默认配置
    print("Testing default configuration...")
    scheduler_default = LODScheduler(stage, camera_path="/World/Camera")
    
    start_time = time.time()
    scheduler_default.build_octree_from_stage(max_depth=6, min_area_threshold=100.0)
    default_build_time = time.time() - start_time
    
    start_time = time.time()
    scheduler_default.update_lod_visibility(fov=60.0, screen_height=1080)
    default_update_time = time.time() - start_time
    
    # 测试性能配置
    print("Testing performance configuration...")
    scheduler_perf = LODScheduler(stage, camera_path="/World/Camera")
    
    start_time = time.time()
    scheduler_perf.build_octree_from_stage(max_depth=4, min_area_threshold=200.0)
    perf_build_time = time.time() - start_time
    
    start_time = time.time()
    scheduler_perf.update_lod_visibility(fov=60.0, screen_height=1080)
    perf_update_time = time.time() - start_time
    
    # 输出性能对比
    print(f"\nPerformance Comparison:")
    print(f"Default config - Build time: {default_build_time:.3f}s, Update time: {default_update_time:.3f}s")
    print(f"Performance config - Build time: {perf_build_time:.3f}s, Update time: {perf_update_time:.3f}s")
    print(f"Build time improvement: {((default_build_time - perf_build_time) / default_build_time * 100):.1f}%")
    print(f"Update time improvement: {((default_update_time - perf_update_time) / default_update_time * 100):.1f}%")

def interactive_example():
    """交互式示例"""
    print("\n=== Interactive LOD Example ===")
    
    stage = omni.usd.get_context().get_stage()
    scheduler = LODScheduler(stage, camera_path="/World/Camera")
    
    # 构建八叉树
    scheduler.build_octree_from_stage(max_depth=5, min_area_threshold=100.0)
    
    print("Interactive LOD system ready!")
    print("You can now move the camera in Omniverse to see LOD changes in real-time.")
    print("The system will automatically update LOD levels based on camera position.")
    
    # 模拟相机移动和LOD更新
    camera_positions = [
        Gf.Vec3f(0, 0, 500),    # 远距离
        Gf.Vec3f(0, 0, 200),    # 中距离
        Gf.Vec3f(0, 0, 50),     # 近距离
        Gf.Vec3f(200, 0, 100),  # 侧视角
        Gf.Vec3f(0, 0, 500)     # 回到远距离
    ]
    
    for i, pos in enumerate(camera_positions):
        print(f"\nCamera position {i+1}: {pos}")
        
        # 更新相机位置（在实际应用中，这通常由用户交互控制）
        camera = stage.GetPrimAtPath("/World/Camera")
        if camera:
            xformable = UsdGeom.Xformable(camera)
            translate_op = xformable.AddTranslateOp()
            translate_op.Set(pos)
        
        # 更新LOD可见性
        scheduler.update_lod_visibility(fov=60.0, screen_height=1080)
        
        # 获取当前相机位置
        current_pos = scheduler.get_camera_position()
        if current_pos:
            print(f"Current camera position: {current_pos}")
    
    print("Interactive example completed!")

def main():
    """主函数"""
    print("LOD Scheduler Examples")
    print("=" * 50)
    
    try:
        # 自定义场景示例（主要示例）
        print("\n" + "="*60)
        print("CUSTOM SCENE EXAMPLE - 4 Regions with 3 LOD Levels Each")
        print("="*60)
        custom_scene_lod_example()
        
        # 测试不同相机位置
        test_camera_positions()
        
        # 性能测试
        performance_test_custom_scene()
        
        # 其他示例（可选）
        print("\n" + "="*60)
        print("ADDITIONAL EXAMPLES")
        print("="*60)
        
        # 基本示例
        basic_lod_example()
        
        # 配置示例
        config_based_example()
        
        # 自定义瓦片示例
        custom_lod_tiles_example()
        
        # 性能对比
        performance_comparison()
        
        # 交互式示例
        interactive_example()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        print("\nCustom Scene Configuration Summary:")
        print("- 4 regions: North, South, East, West")
        print("- Each region has 3 LOD levels: High, Medium, Low")
        print("- Total: 12 LOD tiles")
        print("- Camera positions tested for different LOD switching scenarios")
        print("\nTo use the LOD system in your own project:")
        print("1. Import the LODScheduler class")
        print("2. Create a scheduler instance with your USD stage")
        print("3. Build the octree from your stage")
        print("4. Call update_lod_visibility() when camera moves")
        print("5. Optionally use custom configurations for different use cases")
        
    except Exception as e:
        print(f"Example failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 